namespace RetailInventory.Domain.Entities;

public class Sale
{
    public int Id { get; set; }
    public DateTime SaleDate { get; set; } = DateTime.UtcNow;
    public decimal TotalAmount { get; set; }
    public decimal TotalCost { get; set; }
    public decimal TotalProfit { get; set; }
    public int? CustomerId { get; set; }
    public string Notes { get; set; } = string.Empty;
    public string Source { get; set; } = "Manual"; // Manual, Voice, Receipt
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ICollection<SaleLine> SaleLines { get; set; } = new List<SaleLine>();

    // Business methods
    public void CalculateTotals()
    {
        TotalAmount = SaleLines.Sum(sl => sl.LineTotal);
        TotalCost = SaleLines.Sum(sl => sl.CostPerUnit * sl.Quantity);
        TotalProfit = TotalAmount - TotalCost;
    }

    public void AddSaleLine(SaleLine saleLine)
    {
        SaleLines.Add(saleLine);
        saleLine.Sale = this;
        saleLine.SaleId = Id;
        CalculateTotals();
    }

    public decimal ProfitMargin => TotalAmount > 0 ? (TotalProfit / TotalAmount) * 100 : 0;

    public int TotalItemsSold => SaleLines.Sum(sl => (int)sl.Quantity);
}
