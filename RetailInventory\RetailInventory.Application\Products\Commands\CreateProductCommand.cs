using MediatR;
using RetailInventory.Application.Common;
using RetailInventory.Application.DTOs;
using RetailInventory.Domain.Entities;
using RetailInventory.Domain.Interfaces;

namespace RetailInventory.Application.Products.Commands;

public record CreateProductCommand(CreateProductDto ProductDto) : BaseCommand<Result<int>>;

public class CreateProductCommandHandler : IRequestHandler<CreateProductCommand, Result<int>>
{
    private readonly IUnitOfWork _unitOfWork;

    public CreateProductCommandHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<int>> Handle(CreateProductCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var dto = request.ProductDto;

            // Check if product with same name already exists
            var existingProduct = await _unitOfWork.Products.FirstOrDefaultAsync(p => p.Name == dto.Name);
            if (existingProduct != null)
                return Result.Failure<int>("Product with this name already exists");

            var product = new Product
            {
                Name = dto.Name,
                Category = dto.Category,
                Specifications = dto.Specifications,
                Grade = dto.Grade,
                Color = dto.Color,
                CurrentQuantity = 0,
                ReorderThreshold = dto.ReorderThreshold,
                UnitOfMeasure = dto.UnitOfMeasure,
                IsActive = true
            };

            await _unitOfWork.Products.AddAsync(product);
            await _unitOfWork.SaveChangesAsync();

            // Add metadata if provided
            if (!string.IsNullOrEmpty(dto.MetadataJson))
            {
                var productMeta = new ProductMeta
                {
                    ProductId = product.Id,
                    MetadataJson = dto.MetadataJson
                };

                await _unitOfWork.ProductMetas.AddAsync(productMeta);
                await _unitOfWork.SaveChangesAsync();
            }

            return Result.Success(product.Id);
        }
        catch (Exception ex)
        {
            return Result.Failure<int>($"Error creating product: {ex.Message}");
        }
    }
}
