using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RetailInventory.Domain.Entities;

namespace RetailInventory.Infrastructure.Configurations;

public class SaleConfiguration : IEntityTypeConfiguration<Sale>
{
    public void Configure(EntityTypeBuilder<Sale> builder)
    {
        builder.HasKey(s => s.Id);

        builder.Property(s => s.SaleDate)
            .IsRequired();

        builder.Property(s => s.TotalAmount)
            .IsRequired()
            .HasPrecision(18, 4);

        builder.Property(s => s.TotalCost)
            .IsRequired()
            .HasPrecision(18, 4);

        builder.Property(s => s.TotalProfit)
            .IsRequired()
            .HasPrecision(18, 4);

        builder.Property(s => s.Notes)
            .HasMaxLength(1000);

        builder.Property(s => s.Source)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue("Manual");

        builder.Property(s => s.CreatedAt)
            .IsRequired();

        // Relationships
        builder.HasMany(s => s.SaleLines)
            .WithOne(sl => sl.Sale)
            .HasForeignKey(sl => sl.SaleId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(s => s.SaleDate);
        builder.HasIndex(s => s.Source);
        builder.HasIndex(s => s.CustomerId);
    }
}
