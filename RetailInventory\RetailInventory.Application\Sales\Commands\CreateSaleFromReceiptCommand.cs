using MediatR;
using RetailInventory.Application.Common;
using RetailInventory.Application.DTOs;
using RetailInventory.Application.Services;
using RetailInventory.Domain.Interfaces;

namespace RetailInventory.Application.Sales.Commands;

public record CreateSaleFromReceiptCommand(ReceiptUploadDto ReceiptUpload) : BaseCommand<Result<int>>;

public class CreateSaleFromReceiptCommandHandler : IRequestHandler<CreateSaleFromReceiptCommand, Result<int>>
{
    private readonly IReceiptParserService _receiptParser;
    private readonly IMediator _mediator;

    public CreateSaleFromReceiptCommandHandler(IReceiptParserService receiptParser, IMediator mediator)
    {
        _receiptParser = receiptParser;
        _mediator = mediator;
    }

    public async Task<Result<int>> Handle(CreateSaleFromReceiptCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Parse the receipt
            var parseResult = await _receiptParser.ParseReceiptAsync(request.ReceiptUpload);
            
            if (!parseResult.IsSuccess)
                return Result.Failure<int>($"Receipt parsing failed: {parseResult.ErrorMessage}");

            if (!parseResult.ParsedItems.Any())
                return Result.Failure<int>("No items found in the receipt");

            // Convert parsed items to sale lines
            var saleLines = parseResult.ParsedItems.Select(item => new CreateSaleLineDto
            {
                ProductId = 1, // TODO: Map product names to IDs
                Quantity = item.Quantity,
                SalePricePerUnit = item.UnitPrice
            }).ToList();

            // Create the sale
            var createSaleDto = new CreateSaleDto
            {
                SaleDate = parseResult.ParsedItems.First().Date,
                Source = "Receipt",
                Notes = $"AI-parsed from receipt {parseResult.ReceiptId}. Confidence: {parseResult.ParsedItems.Average(i => i.Confidence):P0}",
                SaleLines = saleLines
            };

            var saleResult = await _mediator.Send(new CreateSaleCommand(createSaleDto), cancellationToken);
            
            if (saleResult.IsFailure)
                return Result.Failure<int>($"Failed to create sale: {saleResult.Error}");

            return Result.Success(saleResult.Value);
        }
        catch (Exception ex)
        {
            return Result.Failure<int>($"Error processing receipt: {ex.Message}");
        }
    }
}
