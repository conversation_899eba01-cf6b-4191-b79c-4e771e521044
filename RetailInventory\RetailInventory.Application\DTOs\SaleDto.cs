namespace RetailInventory.Application.DTOs;

public class SaleDto
{
    public int Id { get; set; }
    public DateTime SaleDate { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal TotalCost { get; set; }
    public decimal TotalProfit { get; set; }
    public decimal ProfitMargin { get; set; }
    public int? CustomerId { get; set; }
    public string Notes { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public int TotalItemsSold { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<SaleLineDto> SaleLines { get; set; } = new();
}

public class SaleLineDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public decimal SalePricePerUnit { get; set; }
    public decimal CostPerUnit { get; set; }
    public decimal LineTotal { get; set; }
    public decimal LineProfit { get; set; }
    public decimal ProfitMargin { get; set; }
}

public class CreateSaleDto
{
    public DateTime? SaleDate { get; set; }
    public int? CustomerId { get; set; }
    public string Notes { get; set; } = string.Empty;
    public string Source { get; set; } = "Manual";
    public List<CreateSaleLineDto> SaleLines { get; set; } = new();
}

public class CreateSaleLineDto
{
    public int ProductId { get; set; }
    public decimal Quantity { get; set; }
    public decimal SalePricePerUnit { get; set; }
}

public class SalesSummaryDto
{
    public DateTime Date { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal TotalCost { get; set; }
    public decimal TotalProfit { get; set; }
    public decimal ProfitMargin { get; set; }
    public int TotalSales { get; set; }
    public int TotalItemsSold { get; set; }
}
