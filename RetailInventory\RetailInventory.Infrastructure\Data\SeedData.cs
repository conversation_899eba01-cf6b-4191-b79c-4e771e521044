using RetailInventory.Domain.Entities;
using RetailInventory.Domain.Enums;
using RetailInventory.Infrastructure.DbContext;
using Microsoft.EntityFrameworkCore;

namespace RetailInventory.Infrastructure.Data;

public static class SeedData
{
    public static async Task SeedAsync(RetailInventoryDbContext context)
    {
        if (await context.Products.AnyAsync())
            return; // Database has been seeded

        // Seed Products for Shoe Shop
        var products = new List<Product>
        {
            new Product
            {
                Name = "Nike Air Max",
                Category = ProductCategory.Footwear,
                Specifications = "Size 8-12",
                Grade = "Premium",
                Color = "Black/White/Red",
                CurrentQuantity = 0,
                ReorderThreshold = 10,
                UnitOfMeasure = UnitOfMeasure.Pairs
            },
            new Product
            {
                Name = "Adidas Ultraboost",
                Category = ProductCategory.Footwear,
                Specifications = "Size 7-11",
                Grade = "Premium",
                Color = "Black/Blue/Grey",
                CurrentQuantity = 0,
                ReorderThreshold = 8,
                UnitOfMeasure = UnitOfMeasure.Pairs
            },
            new Product
            {
                Name = "Formal Oxford Shoes",
                Category = ProductCategory.Footwear,
                Specifications = "Size 6-12",
                Grade = "Standard",
                Color = "Brown/Black",
                CurrentQuantity = 0,
                ReorderThreshold = 15,
                UnitOfMeasure = UnitOfMeasure.Pairs
            },
            new Product
            {
                Name = "Women's Heels",
                Category = ProductCategory.Footwear,
                Specifications = "Size 5-9",
                Grade = "Premium",
                Color = "Red/Black/Nude",
                CurrentQuantity = 0,
                ReorderThreshold = 12,
                UnitOfMeasure = UnitOfMeasure.Pairs
            },
            new Product
            {
                Name = "Casual Sneakers",
                Category = ProductCategory.Footwear,
                Specifications = "Size 6-11",
                Grade = "Standard",
                Color = "Various Colors",
                CurrentQuantity = 0,
                ReorderThreshold = 20,
                UnitOfMeasure = UnitOfMeasure.Pairs
            },
            new Product
            {
                Name = "Sports Running Shoes",
                Category = ProductCategory.Footwear,
                Specifications = "Size 7-12",
                Grade = "Premium",
                Color = "Multi-color",
                CurrentQuantity = 0,
                ReorderThreshold = 15,
                UnitOfMeasure = UnitOfMeasure.Pairs
            }
        };

        await context.Products.AddRangeAsync(products);
        await context.SaveChangesAsync();

        // Seed Stock Batches
        var stockBatches = new List<StockBatch>();
        var random = new Random();

        foreach (var product in products)
        {
            // Create 2-3 batches per product
            for (int i = 0; i < random.Next(2, 4); i++)
            {
                var batch = new StockBatch
                {
                    ProductId = product.Id,
                    CostPerUnit = GetCostForProduct(product.Name),
                    Quantity = random.Next(10, 30),
                    BatchNumber = $"BATCH-{product.Id}-{i + 1:D3}",
                    ReceivedDate = DateTime.UtcNow.AddDays(-random.Next(1, 60))
                };
                batch.RemainingQuantity = batch.Quantity;
                stockBatches.Add(batch);

                // Update product quantity
                product.CurrentQuantity += batch.Quantity;
            }
        }

        await context.StockBatches.AddRangeAsync(stockBatches);
        context.Products.UpdateRange(products);
        await context.SaveChangesAsync();

        // Seed Product Metadata
        var productMetas = new List<ProductMeta>
        {
            new ProductMeta
            {
                ProductId = products[0].Id, // Nike Air Max
                MetadataJson = """{"Brand": "Nike", "Size": "10", "Color": "Black", "Material": "Mesh/Rubber", "Type": "Running"}"""
            },
            new ProductMeta
            {
                ProductId = products[1].Id, // Adidas Ultraboost
                MetadataJson = """{"Brand": "Adidas", "Size": "9", "Color": "White", "Material": "Knit/Boost", "Type": "Lifestyle"}"""
            },
            new ProductMeta
            {
                ProductId = products[2].Id, // Formal Oxford Shoes
                MetadataJson = """{"Brand": "Clarks", "Size": "8", "Color": "Brown", "Material": "Leather", "Type": "Formal"}"""
            }
        };

        await context.ProductMetas.AddRangeAsync(productMetas);
        await context.SaveChangesAsync();
    }

    private static decimal GetCostForProduct(string productName)
    {
        return productName switch
        {
            "Nike Air Max" => 3200m,
            "Adidas Ultraboost" => 4500m,
            "Formal Oxford Shoes" => 2200m,
            "Women's Heels" => 1800m,
            "Casual Sneakers" => 1200m,
            "Sports Running Shoes" => 2500m,
            _ => 1000m
        };
    }
}
