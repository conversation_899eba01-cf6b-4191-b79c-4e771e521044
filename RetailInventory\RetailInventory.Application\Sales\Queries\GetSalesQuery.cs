using MediatR;
using RetailInventory.Application.Common;
using RetailInventory.Application.DTOs;
using RetailInventory.Domain.Interfaces;

namespace RetailInventory.Application.Sales.Queries;

public record GetSalesQuery(DateTime? FromDate = null, DateTime? ToDate = null, int Skip = 0, int Take = 50) : BaseQuery<Result<List<SaleDto>>>;

public class GetSalesQueryHandler : IRequestHandler<GetSalesQuery, Result<List<SaleDto>>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetSalesQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<List<SaleDto>>> Handle(GetSalesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var sales = await _unitOfWork.Sales.GetAllAsync();
            
            // Apply date filters
            if (request.FromDate.HasValue)
                sales = sales.Where(s => s.SaleDate >= request.FromDate.Value);
            
            if (request.ToDate.HasValue)
                sales = sales.Where(s => s.SaleDate <= request.ToDate.Value);

            // Apply pagination
            var pagedSales = sales
                .OrderByDescending(s => s.SaleDate)
                .Skip(request.Skip)
                .Take(request.Take)
                .ToList();

            var saleDtos = new List<SaleDto>();

            foreach (var sale in pagedSales)
            {
                var saleLines = await _unitOfWork.SaleLines.FindAsync(sl => sl.SaleId == sale.Id);
                var saleLineDtos = new List<SaleLineDto>();

                foreach (var saleLine in saleLines)
                {
                    var product = await _unitOfWork.Products.GetByIdAsync(saleLine.ProductId);
                    
                    saleLineDtos.Add(new SaleLineDto
                    {
                        Id = saleLine.Id,
                        ProductId = saleLine.ProductId,
                        ProductName = product?.Name ?? "Unknown",
                        Quantity = saleLine.Quantity,
                        SalePricePerUnit = saleLine.SalePricePerUnit,
                        CostPerUnit = saleLine.CostPerUnit,
                        LineTotal = saleLine.LineTotal,
                        LineProfit = saleLine.LineProfit,
                        ProfitMargin = saleLine.ProfitMargin
                    });
                }

                saleDtos.Add(new SaleDto
                {
                    Id = sale.Id,
                    SaleDate = sale.SaleDate,
                    TotalAmount = sale.TotalAmount,
                    TotalCost = sale.TotalCost,
                    TotalProfit = sale.TotalProfit,
                    ProfitMargin = sale.ProfitMargin,
                    CustomerId = sale.CustomerId,
                    Notes = sale.Notes,
                    Source = sale.Source,
                    TotalItemsSold = sale.TotalItemsSold,
                    CreatedAt = sale.CreatedAt,
                    SaleLines = saleLineDtos
                });
            }

            return Result.Success(saleDtos);
        }
        catch (Exception ex)
        {
            return Result.Failure<List<SaleDto>>($"Error retrieving sales: {ex.Message}");
        }
    }
}
