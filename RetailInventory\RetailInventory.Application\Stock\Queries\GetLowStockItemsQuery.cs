using MediatR;
using RetailInventory.Application.Common;
using RetailInventory.Application.DTOs;
using RetailInventory.Domain.Interfaces;

namespace RetailInventory.Application.Stock.Queries;

public record GetLowStockItemsQuery : BaseQuery<Result<List<LowStockItemDto>>>;

public class GetLowStockItemsQueryHandler : IRequestHandler<GetLowStockItemsQuery, Result<List<LowStockItemDto>>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetLowStockItemsQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<List<LowStockItemDto>>> Handle(GetLowStockItemsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var products = await _unitOfWork.Products.FindAsync(p => 
                p.IsActive && p.CurrentQuantity <= p.ReorderThreshold);

            var lowStockItems = products.Select(p => new LowStockItemDto
            {
                ProductId = p.Id,
                ProductName = p.Name,
                CurrentQuantity = p.CurrentQuantity,
                ReorderThreshold = p.ReorderThreshold,
                UnitOfMeasure = p.UnitOfMeasure.ToString(),
                SuggestedOrderQuantity = Math.Max(p.ReorderThreshold * 2 - p.CurrentQuantity, p.ReorderThreshold)
            }).ToList();

            return Result.Success(lowStockItems);
        }
        catch (Exception ex)
        {
            return Result.Failure<List<LowStockItemDto>>($"Error retrieving low stock items: {ex.Message}");
        }
    }
}
