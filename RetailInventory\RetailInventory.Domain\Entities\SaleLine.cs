namespace RetailInventory.Domain.Entities;

public class SaleLine
{
    public int Id { get; set; }
    public int SaleId { get; set; }
    public int ProductId { get; set; }
    public decimal Quantity { get; set; }
    public decimal SalePricePerUnit { get; set; }
    public decimal CostPerUnit { get; set; }
    public decimal LineTotal { get; set; }
    public decimal LineProfit { get; set; }

    // Navigation properties
    public virtual Sale Sale { get; set; } = null!;
    public virtual Product Product { get; set; } = null!;

    // Business methods
    public void CalculateLineValues()
    {
        LineTotal = Quantity * SalePricePerUnit;
        LineProfit = LineTotal - (Quantity * CostPerUnit);
    }

    public decimal ProfitMargin => LineTotal > 0 ? (LineProfit / LineTotal) * 100 : 0;
}
