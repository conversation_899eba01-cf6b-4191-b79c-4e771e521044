using RetailInventory.Application.DTOs;

namespace RetailInventory.Application.Services;

public interface IReorderRadarService
{
    Task<ReorderRadarResultDto> GenerateReorderSuggestionsAsync();
    Task<List<SalesVelocityDto>> CalculateSalesVelocityAsync();
    Task<ReorderSuggestionDto> GetProductReorderSuggestionAsync(int productId);
    Task<decimal> CalculateOptimalReorderPointAsync(int productId, int leadTimeDays = 7);
    Task<List<ReorderSuggestionDto>> GetCriticalStockAlertsAsync();
}

public class ReorderRadarException : Exception
{
    public ReorderRadarException(string message) : base(message) { }
    public ReorderRadarException(string message, Exception innerException) : base(message, innerException) { }
}
