using RetailInventory.Domain.Enums;

namespace RetailInventory.Application.DTOs;

public class ProductDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public ProductCategory Category { get; set; }
    public string Specifications { get; set; } = string.Empty;
    public string Grade { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public decimal CurrentQuantity { get; set; }
    public decimal ReorderThreshold { get; set; }
    public UnitOfMeasure UnitOfMeasure { get; set; }
    public bool IsActive { get; set; }
    public bool IsLowStock { get; set; }
    public decimal AverageCostPerUnit { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? MetadataJson { get; set; }
}

public class CreateProductDto
{
    public string Name { get; set; } = string.Empty;
    public ProductCategory Category { get; set; }
    public string Specifications { get; set; } = string.Empty;
    public string Grade { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public decimal ReorderThreshold { get; set; }
    public UnitOfMeasure UnitOfMeasure { get; set; }
    public string? MetadataJson { get; set; }
}

public class UpdateProductDto
{
    public string Name { get; set; } = string.Empty;
    public ProductCategory Category { get; set; }
    public string Specifications { get; set; } = string.Empty;
    public string Grade { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public decimal ReorderThreshold { get; set; }
    public UnitOfMeasure UnitOfMeasure { get; set; }
    public bool IsActive { get; set; }
    public string? MetadataJson { get; set; }
}
