using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RetailInventory.Domain.Entities;

namespace RetailInventory.Infrastructure.Configurations;

public class ProductMetaConfiguration : IEntityTypeConfiguration<ProductMeta>
{
    public void Configure(EntityTypeBuilder<ProductMeta> builder)
    {
        builder.HasKey(pm => pm.Id);

        builder.Property(pm => pm.MetadataJson)
            .IsRequired()
            .HasColumnType("nvarchar(max)")
            .HasDefaultValue("{}");

        // Indexes
        builder.HasIndex(pm => pm.ProductId)
            .IsUnique();
    }
}
