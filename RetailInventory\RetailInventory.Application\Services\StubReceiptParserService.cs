using RetailInventory.Application.DTOs;

namespace RetailInventory.Application.Services;

public class StubReceiptParserService : IReceiptParserService
{
    private readonly Random _random = new();
    
    private readonly List<ParsedReceiptDto> _sampleReceipts = new()
    {
        new ParsedReceiptDto
        {
            ProductType = "Nike Air Max",
            Quantity = 2,
            UnitPrice = 4500m,
            Date = DateTime.Now.AddDays(-1),
            Confidence = 0.95m,
            Brand = "Nike",
            Size = "10",
            Color = "Black",
            Notes = "Premium running shoes"
        },
        new ParsedReceiptDto
        {
            ProductType = "Adidas Ultraboost",
            Quantity = 1,
            UnitPrice = 6200m,
            Date = DateTime.Now.AddDays(-2),
            Confidence = 0.92m,
            Brand = "Adidas",
            Size = "9",
            Color = "White",
            Notes = "Lifestyle sneakers"
        },
        new ParsedReceiptDto
        {
            ProductType = "Formal Oxford Shoes",
            Quantity = 3,
            UnitPrice = 3200m,
            Date = DateTime.Now.AddDays(-1),
            Confidence = 0.88m,
            <PERSON> = "Clark<PERSON>",
            Size = "8",
            Color = "Brown",
            Notes = "Leather formal shoes"
        },
        new ParsedReceiptDto
        {
            ProductType = "Women's Heels",
            Quantity = 2,
            UnitPrice = 2800m,
            Date = DateTime.Now,
            Confidence = 0.91m,
            Brand = "Steve Madden",
            Size = "7",
            Color = "Red",
            Notes = "High heel party shoes"
        },
        new ParsedReceiptDto
        {
            ProductType = "Casual Sneakers",
            Quantity = 4,
            UnitPrice = 1800m,
            Date = DateTime.Now,
            Confidence = 0.89m,
            Brand = "Converse",
            Size = "9",
            Color = "Various",
            Notes = "Canvas casual shoes"
        }
    };

    public async Task<ReceiptParseResultDto> ParseReceiptAsync(ReceiptUploadDto receiptUpload)
    {
        // Simulate processing delay
        await Task.Delay(_random.Next(1000, 3000));

        try
        {
            // Simulate validation
            if (!await ValidateReceiptFormatAsync(receiptUpload))
            {
                return new ReceiptParseResultDto
                {
                    IsSuccess = false,
                    ErrorMessage = "Invalid receipt format. Please upload a clear image of a retail receipt.",
                    ReceiptId = Guid.NewGuid().ToString("N")[..8]
                };
            }

            // Generate realistic parsed data
            var itemCount = _random.Next(1, 4);
            var parsedItems = new List<ParsedReceiptDto>();
            
            for (int i = 0; i < itemCount; i++)
            {
                var sampleItem = _sampleReceipts[_random.Next(_sampleReceipts.Count)];
                var parsedItem = new ParsedReceiptDto
                {
                    ProductType = sampleItem.ProductType,
                    Quantity = _random.Next(1, 5),
                    UnitPrice = sampleItem.UnitPrice + _random.Next(-500, 500),
                    Date = DateTime.Now.AddDays(-_random.Next(0, 7)),
                    Confidence = 0.85m + (decimal)(_random.NextDouble() * 0.15),
                    Brand = sampleItem.Brand,
                    Size = _random.Next(6, 13).ToString(),
                    Color = GetRandomColor(),
                    Notes = $"AI-parsed from receipt - {sampleItem.Notes}"
                };
                parsedItems.Add(parsedItem);
            }

            var totalAmount = parsedItems.Sum(item => item.Quantity * item.UnitPrice);

            return new ReceiptParseResultDto
            {
                IsSuccess = true,
                ParsedItems = parsedItems,
                TotalAmount = totalAmount,
                ReceiptId = Guid.NewGuid().ToString("N")[..8]
            };
        }
        catch (Exception ex)
        {
            return new ReceiptParseResultDto
            {
                IsSuccess = false,
                ErrorMessage = $"Receipt parsing failed: {ex.Message}",
                ReceiptId = Guid.NewGuid().ToString("N")[..8]
            };
        }
    }

    public async Task<ReceiptParseResultDto> ParseReceiptFromUrlAsync(string imageUrl)
    {
        // Simulate URL validation and download
        await Task.Delay(_random.Next(500, 1500));
        
        var mockUpload = new ReceiptUploadDto
        {
            ImageBase64 = "mock_base64_from_url",
            FileName = Path.GetFileName(imageUrl),
            ContentType = "image/jpeg"
        };

        return await ParseReceiptAsync(mockUpload);
    }

    public async Task<bool> ValidateReceiptFormatAsync(ReceiptUploadDto receiptUpload)
    {
        await Task.Delay(200); // Simulate validation processing
        
        // Basic validation checks
        if (string.IsNullOrEmpty(receiptUpload.ImageBase64))
            return false;
            
        if (string.IsNullOrEmpty(receiptUpload.ContentType))
            return false;
            
        // Simulate 95% success rate
        return _random.NextDouble() > 0.05;
    }

    private string GetRandomColor()
    {
        var colors = new[] { "Black", "White", "Brown", "Red", "Blue", "Gray", "Navy", "Tan", "Green" };
        return colors[_random.Next(colors.Length)];
    }
}
