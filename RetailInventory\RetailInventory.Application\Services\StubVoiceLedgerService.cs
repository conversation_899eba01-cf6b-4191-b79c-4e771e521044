using RetailInventory.Application.DTOs;
using System.Text.RegularExpressions;

namespace RetailInventory.Application.Services;

public class StubVoiceLedgerService : IVoiceLedgerService
{
    private readonly Random _random = new();
    
    private readonly List<VoiceCommandPatternDto> _supportedPatterns = new()
    {
        new VoiceCommandPatternDto
        {
            Pattern = "Add [quantity] [unit] [product] at [price] per [unit]",
            Example = "Add 5 pairs Nike sneakers at 2500 rupees each",
            Description = "Add product sale with quantity and price"
        },
        new VoiceCommandPatternDto
        {
            Pattern = "Sell [quantity] [product] for [total_price]",
            Example = "Sell 3 Adidas shoes for 18000 rupees",
            Description = "Record sale with total amount"
        },
        new VoiceCommandPatternDto
        {
            Pattern = "Record sale [quantity] [product] [size] [color]",
            Example = "Record sale 2 Nike Air Max size 10 black color",
            Description = "Record detailed product sale"
        }
    };

    private readonly Dictionary<string, string[]> _productSynonyms = new()
    {
        { "nike", new[] { "nike", "nike shoes", "nike sneakers", "air max", "nike air" } },
        { "adidas", new[] { "adidas", "adidas shoes", "ultraboost", "adidas sneakers" } },
        { "formal", new[] { "formal shoes", "oxford", "dress shoes", "office shoes" } },
        { "heels", new[] { "heels", "high heels", "women heels", "ladies shoes" } },
        { "sneakers", new[] { "sneakers", "casual shoes", "sports shoes", "running shoes" } }
    };

    public async Task<VoiceCommandResultDto> ProcessVoiceCommandAsync(VoiceCommandDto voiceCommand)
    {
        try
        {
            // Simulate audio transcription
            await Task.Delay(_random.Next(1000, 2500));
            
            string transcribedText;
            if (!string.IsNullOrEmpty(voiceCommand.TranscribedText))
            {
                transcribedText = voiceCommand.TranscribedText;
            }
            else
            {
                transcribedText = await TranscribeAudioAsync(voiceCommand.AudioBase64);
            }

            return await ProcessTextCommandAsync(transcribedText);
        }
        catch (Exception ex)
        {
            return new VoiceCommandResultDto
            {
                IsSuccess = false,
                ErrorMessage = $"Voice processing failed: {ex.Message}",
                CommandId = Guid.NewGuid().ToString("N")[..8]
            };
        }
    }

    public async Task<VoiceCommandResultDto> ProcessTextCommandAsync(string textCommand)
    {
        await Task.Delay(_random.Next(300, 800)); // Simulate processing

        try
        {
            var parsedSale = ParseTextCommand(textCommand.ToLowerInvariant());
            
            if (parsedSale == null)
            {
                return new VoiceCommandResultDto
                {
                    IsSuccess = false,
                    ErrorMessage = "Could not understand the command. Please try again with a clearer instruction.",
                    TranscribedText = textCommand,
                    Confidence = 0.3m,
                    CommandId = Guid.NewGuid().ToString("N")[..8]
                };
            }

            return new VoiceCommandResultDto
            {
                IsSuccess = true,
                TranscribedText = textCommand,
                ParsedSale = parsedSale,
                Confidence = 0.85m + (decimal)(_random.NextDouble() * 0.15),
                CommandId = Guid.NewGuid().ToString("N")[..8]
            };
        }
        catch (Exception ex)
        {
            return new VoiceCommandResultDto
            {
                IsSuccess = false,
                ErrorMessage = $"Text parsing failed: {ex.Message}",
                TranscribedText = textCommand,
                CommandId = Guid.NewGuid().ToString("N")[..8]
            };
        }
    }

    public async Task<List<VoiceCommandPatternDto>> GetSupportedPatternsAsync()
    {
        await Task.Delay(100);
        return _supportedPatterns;
    }

    public async Task<string> TranscribeAudioAsync(string audioBase64)
    {
        // Simulate transcription processing
        await Task.Delay(_random.Next(1500, 3000));
        
        // Return realistic sample transcriptions
        var sampleTranscriptions = new[]
        {
            "Add 5 pairs Nike sneakers size 42 at 2500 rupees each",
            "Sell 3 Adidas Ultraboost for 18600 rupees",
            "Record sale 2 formal Oxford shoes brown color size 9",
            "Add 4 pairs women heels red color at 2800 per pair",
            "Sell 1 Nike Air Max black size 10 for 4500 rupees"
        };

        return sampleTranscriptions[_random.Next(sampleTranscriptions.Length)];
    }

    private VoiceParsedSaleDto? ParseTextCommand(string command)
    {
        // Pattern 1: "add [quantity] [unit] [product] at [price] per [unit]"
        var pattern1 = @"add\s+(\d+)\s+(pairs?|pieces?|kg|kilograms?)\s+(.+?)\s+at\s+(\d+)\s+rupees?\s+(each|per\s+\w+)";
        var match1 = Regex.Match(command, pattern1, RegexOptions.IgnoreCase);
        
        if (match1.Success)
        {
            var quantity = decimal.Parse(match1.Groups[1].Value);
            var unit = match1.Groups[2].Value;
            var productText = match1.Groups[3].Value;
            var price = decimal.Parse(match1.Groups[4].Value);
            
            var productInfo = ExtractProductInfo(productText);
            
            return new VoiceParsedSaleDto
            {
                ProductName = productInfo.Name,
                Quantity = quantity,
                Unit = unit,
                PricePerUnit = price,
                Size = productInfo.Size,
                Color = productInfo.Color,
                Brand = productInfo.Brand,
                TotalAmount = quantity * price,
                Notes = $"Voice command: {command}"
            };
        }

        // Pattern 2: "sell [quantity] [product] for [total_price]"
        var pattern2 = @"sell\s+(\d+)\s+(.+?)\s+for\s+(\d+)\s+rupees?";
        var match2 = Regex.Match(command, pattern2, RegexOptions.IgnoreCase);
        
        if (match2.Success)
        {
            var quantity = decimal.Parse(match2.Groups[1].Value);
            var productText = match2.Groups[2].Value;
            var totalPrice = decimal.Parse(match2.Groups[3].Value);
            
            var productInfo = ExtractProductInfo(productText);
            
            return new VoiceParsedSaleDto
            {
                ProductName = productInfo.Name,
                Quantity = quantity,
                Unit = "pairs",
                PricePerUnit = totalPrice / quantity,
                Size = productInfo.Size,
                Color = productInfo.Color,
                Brand = productInfo.Brand,
                TotalAmount = totalPrice,
                Notes = $"Voice command: {command}"
            };
        }

        // Pattern 3: "record sale [quantity] [product] [details]"
        var pattern3 = @"record\s+sale\s+(\d+)\s+(.+)";
        var match3 = Regex.Match(command, pattern3, RegexOptions.IgnoreCase);
        
        if (match3.Success)
        {
            var quantity = decimal.Parse(match3.Groups[1].Value);
            var productText = match3.Groups[2].Value;
            
            var productInfo = ExtractProductInfo(productText);
            var estimatedPrice = EstimatePrice(productInfo.Name);
            
            return new VoiceParsedSaleDto
            {
                ProductName = productInfo.Name,
                Quantity = quantity,
                Unit = "pairs",
                PricePerUnit = estimatedPrice,
                Size = productInfo.Size,
                Color = productInfo.Color,
                Brand = productInfo.Brand,
                TotalAmount = quantity * estimatedPrice,
                Notes = $"Voice command: {command}"
            };
        }

        return null;
    }

    private (string Name, string Size, string Color, string Brand) ExtractProductInfo(string productText)
    {
        var name = "Unknown Product";
        var size = "";
        var color = "";
        var brand = "";

        // Extract size
        var sizeMatch = Regex.Match(productText, @"size\s+(\d+)", RegexOptions.IgnoreCase);
        if (sizeMatch.Success)
        {
            size = sizeMatch.Groups[1].Value;
            productText = productText.Replace(sizeMatch.Value, "").Trim();
        }

        // Extract color
        var colors = new[] { "black", "white", "brown", "red", "blue", "gray", "navy", "tan", "green" };
        foreach (var c in colors)
        {
            if (productText.Contains(c, StringComparison.OrdinalIgnoreCase))
            {
                color = c;
                productText = productText.Replace(c, "", StringComparison.OrdinalIgnoreCase).Trim();
                break;
            }
        }

        // Extract brand and product name
        foreach (var kvp in _productSynonyms)
        {
            foreach (var synonym in kvp.Value)
            {
                if (productText.Contains(synonym, StringComparison.OrdinalIgnoreCase))
                {
                    brand = kvp.Key.ToTitleCase();
                    name = GetProductNameForBrand(kvp.Key);
                    break;
                }
            }
            if (!string.IsNullOrEmpty(brand)) break;
        }

        if (string.IsNullOrEmpty(brand))
        {
            name = productText.ToTitleCase();
        }

        return (name, size, color.ToTitleCase(), brand);
    }

    private string GetProductNameForBrand(string brand)
    {
        return brand.ToLowerInvariant() switch
        {
            "nike" => "Nike Air Max",
            "adidas" => "Adidas Ultraboost",
            "formal" => "Formal Oxford Shoes",
            "heels" => "Women's Heels",
            "sneakers" => "Casual Sneakers",
            _ => $"{brand.ToTitleCase()} Shoes"
        };
    }

    private decimal EstimatePrice(string productName)
    {
        return productName.ToLowerInvariant() switch
        {
            var name when name.Contains("nike") => 4500m,
            var name when name.Contains("adidas") => 6200m,
            var name when name.Contains("formal") => 3200m,
            var name when name.Contains("heels") => 2800m,
            var name when name.Contains("sneakers") => 1800m,
            _ => 2500m
        };
    }
}

public static class StringExtensions
{
    public static string ToTitleCase(this string input)
    {
        if (string.IsNullOrEmpty(input)) return input;
        return char.ToUpper(input[0]) + input[1..].ToLower();
    }
}
