using Microsoft.EntityFrameworkCore.Storage;
using RetailInventory.Domain.Entities;
using RetailInventory.Domain.Interfaces;
using RetailInventory.Infrastructure.DbContext;

namespace RetailInventory.Infrastructure.Repositories;

public class UnitOfWork : IUnitOfWork
{
    private readonly RetailInventoryDbContext _context;
    private IDbContextTransaction? _transaction;

    public UnitOfWork(RetailInventoryDbContext context)
    {
        _context = context;
        Products = new Repository<Product>(_context);
        StockBatches = new Repository<StockBatch>(_context);
        Sales = new Repository<Sale>(_context);
        SaleLines = new Repository<SaleLine>(_context);
        ProductMetas = new Repository<ProductMeta>(_context);
    }

    public IRepository<Product> Products { get; }
    public IRepository<StockBatch> StockBatches { get; }
    public IRepository<Sale> Sales { get; }
    public IRepository<SaleLine> SaleLines { get; }
    public IRepository<ProductMeta> ProductMetas { get; }

    public async Task<int> SaveChangesAsync()
    {
        return await _context.SaveChangesAsync();
    }

    public async Task BeginTransactionAsync()
    {
        _transaction = await _context.Database.BeginTransactionAsync();
    }

    public async Task CommitTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context.Dispose();
    }
}
