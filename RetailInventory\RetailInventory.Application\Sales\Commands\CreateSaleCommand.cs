using MediatR;
using RetailInventory.Application.Common;
using RetailInventory.Application.DTOs;
using RetailInventory.Domain.Entities;
using RetailInventory.Domain.Interfaces;

namespace RetailInventory.Application.Sales.Commands;

public record CreateSaleCommand(CreateSaleDto SaleDto) : BaseCommand<Result<int>>;

public class CreateSaleCommandHandler : IRequestHandler<CreateSaleCommand, Result<int>>
{
    private readonly IUnitOfWork _unitOfWork;

    public CreateSaleCommandHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<int>> Handle(CreateSaleCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var dto = request.SaleDto;

            await _unitOfWork.BeginTransactionAsync();

            // Create sale
            var sale = new Sale
            {
                SaleDate = dto.SaleDate ?? DateTime.UtcNow,
                CustomerId = dto.CustomerId,
                Notes = dto.Notes,
                Source = dto.Source
            };

            await _unitOfWork.Sales.AddAsync(sale);
            await _unitOfWork.SaveChangesAsync();

            // Process sale lines
            foreach (var lineDto in dto.SaleLines)
            {
                var product = await _unitOfWork.Products.GetByIdAsync(lineDto.ProductId);
                if (product == null)
                {
                    await _unitOfWork.RollbackTransactionAsync();
                    return Result.Failure<int>($"Product with ID {lineDto.ProductId} not found");
                }

                if (product.CurrentQuantity < lineDto.Quantity)
                {
                    await _unitOfWork.RollbackTransactionAsync();
                    return Result.Failure<int>($"Insufficient stock for product {product.Name}. Available: {product.CurrentQuantity}, Required: {lineDto.Quantity}");
                }

                // Calculate cost using FIFO
                var costPerUnit = await CalculateFIFOCost(lineDto.ProductId, lineDto.Quantity);

                var saleLine = new SaleLine
                {
                    SaleId = sale.Id,
                    ProductId = lineDto.ProductId,
                    Quantity = lineDto.Quantity,
                    SalePricePerUnit = lineDto.SalePricePerUnit,
                    CostPerUnit = costPerUnit
                };

                saleLine.CalculateLineValues();
                await _unitOfWork.SaleLines.AddAsync(saleLine);

                // Reduce stock using FIFO
                await ReduceStockFIFO(lineDto.ProductId, lineDto.Quantity);

                // Update product quantity
                product.ReduceStock(lineDto.Quantity);
                _unitOfWork.Products.Update(product);
            }

            await _unitOfWork.SaveChangesAsync();

            // Recalculate sale totals
            var saleLines = await _unitOfWork.SaleLines.FindAsync(sl => sl.SaleId == sale.Id);
            sale.TotalAmount = saleLines.Sum(sl => sl.LineTotal);
            sale.TotalCost = saleLines.Sum(sl => sl.CostPerUnit * sl.Quantity);
            sale.TotalProfit = sale.TotalAmount - sale.TotalCost;

            _unitOfWork.Sales.Update(sale);
            await _unitOfWork.SaveChangesAsync();

            await _unitOfWork.CommitTransactionAsync();

            return Result.Success(sale.Id);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            return Result.Failure<int>($"Error creating sale: {ex.Message}");
        }
    }

    private async Task<decimal> CalculateFIFOCost(int productId, decimal quantity)
    {
        var batches = await _unitOfWork.StockBatches.FindAsync(sb => 
            sb.ProductId == productId && sb.RemainingQuantity > 0);
        
        var orderedBatches = batches.OrderBy(sb => sb.ReceivedDate).ToList();
        
        decimal totalCost = 0;
        decimal remainingQuantity = quantity;

        foreach (var batch in orderedBatches)
        {
            if (remainingQuantity <= 0) break;

            var quantityFromBatch = Math.Min(remainingQuantity, batch.RemainingQuantity);
            totalCost += quantityFromBatch * batch.CostPerUnit;
            remainingQuantity -= quantityFromBatch;
        }

        return quantity > 0 ? totalCost / quantity : 0;
    }

    private async Task ReduceStockFIFO(int productId, decimal quantity)
    {
        var batches = await _unitOfWork.StockBatches.FindAsync(sb => 
            sb.ProductId == productId && sb.RemainingQuantity > 0);
        
        var orderedBatches = batches.OrderBy(sb => sb.ReceivedDate).ToList();
        
        decimal remainingQuantity = quantity;

        foreach (var batch in orderedBatches)
        {
            if (remainingQuantity <= 0) break;

            var quantityFromBatch = Math.Min(remainingQuantity, batch.RemainingQuantity);
            batch.ReduceQuantity(quantityFromBatch);
            _unitOfWork.StockBatches.Update(batch);
            remainingQuantity -= quantityFromBatch;
        }
    }
}
