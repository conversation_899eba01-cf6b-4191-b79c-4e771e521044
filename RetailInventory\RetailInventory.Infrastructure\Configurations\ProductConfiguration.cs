using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RetailInventory.Domain.Entities;
using RetailInventory.Domain.Enums;

namespace RetailInventory.Infrastructure.Configurations;

public class ProductConfiguration : IEntityTypeConfiguration<Product>
{
    public void Configure(EntityTypeBuilder<Product> builder)
    {
        builder.HasKey(p => p.Id);

        builder.Property(p => p.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(p => p.Category)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(p => p.Specifications)
            .HasMaxLength(500);

        builder.Property(p => p.Grade)
            .HasMaxLength(100);

        builder.Property(p => p.Color)
            .HasMaxLength(100);

        builder.Property(p => p.CurrentQuantity)
            .HasPrecision(18, 4);

        builder.Property(p => p.ReorderThreshold)
            .HasPrecision(18, 4);

        builder.Property(p => p.UnitOfMeasure)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(p => p.CreatedAt)
            .IsRequired();

        builder.Property(p => p.UpdatedAt)
            .IsRequired();

        // Relationships
        builder.HasMany(p => p.StockBatches)
            .WithOne(sb => sb.Product)
            .HasForeignKey(sb => sb.ProductId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(p => p.SaleLines)
            .WithOne(sl => sl.Product)
            .HasForeignKey(sl => sl.ProductId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(p => p.ProductMeta)
            .WithOne(pm => pm.Product)
            .HasForeignKey<ProductMeta>(pm => pm.ProductId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(p => p.Name);
        builder.HasIndex(p => p.Category);
        builder.HasIndex(p => p.IsActive);
    }
}
