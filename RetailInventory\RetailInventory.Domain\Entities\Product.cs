using RetailInventory.Domain.Enums;
using RetailInventory.Domain.ValueObjects;

namespace RetailInventory.Domain.Entities;

public class Product
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public ProductCategory Category { get; set; }
    public string Specifications { get; set; } = string.Empty; // Size/dimensions
    public string Grade { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public decimal CurrentQuantity { get; set; }
    public decimal ReorderThreshold { get; set; }
    public UnitOfMeasure UnitOfMeasure { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ICollection<StockBatch> StockBatches { get; set; } = new List<StockBatch>();
    public virtual ICollection<SaleLine> SaleLines { get; set; } = new List<SaleLine>();
    public virtual ProductMeta? ProductMeta { get; set; }

    // Business methods
    public void UpdateQuantity(decimal newQuantity)
    {
        if (newQuantity < 0)
            throw new ArgumentException("Quantity cannot be negative");
        
        CurrentQuantity = newQuantity;
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddStock(decimal quantity)
    {
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be positive");
        
        CurrentQuantity += quantity;
        UpdatedAt = DateTime.UtcNow;
    }

    public void ReduceStock(decimal quantity)
    {
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be positive");
        
        if (CurrentQuantity < quantity)
            throw new InvalidOperationException("Insufficient stock");
        
        CurrentQuantity -= quantity;
        UpdatedAt = DateTime.UtcNow;
    }

    public bool IsLowStock => CurrentQuantity <= ReorderThreshold;

    public decimal GetAverageCostPerUnit()
    {
        var activeBatches = StockBatches.Where(b => b.RemainingQuantity > 0).ToList();
        if (!activeBatches.Any())
            return 0;

        var totalValue = activeBatches.Sum(b => b.CostPerUnit * b.RemainingQuantity);
        var totalQuantity = activeBatches.Sum(b => b.RemainingQuantity);
        
        return totalQuantity > 0 ? totalValue / totalQuantity : 0;
    }
}
