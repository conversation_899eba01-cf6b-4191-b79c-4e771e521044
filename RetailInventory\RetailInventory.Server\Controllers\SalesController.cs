using MediatR;
using Microsoft.AspNetCore.Mvc;
using RetailInventory.Application.DTOs;
using RetailInventory.Application.Sales.Commands;
using RetailInventory.Application.Sales.Queries;

namespace RetailInventory.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SalesController : ControllerBase
{
    private readonly IMediator _mediator;

    public SalesController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost("manual")]
    public async Task<IActionResult> CreateManualSale([FromBody] CreateSaleDto saleDto)
    {
        saleDto.Source = "Manual";
        var result = await _mediator.Send(new CreateSaleCommand(saleDto));
        
        if (result.IsFailure)
            return BadRequest(result.Error);
        
        return Ok(new { SaleId = result.Value });
    }

    [HttpPost("from-receipt")]
    public async Task<IActionResult> CreateSaleFromReceipt([FromBody] CreateSaleDto saleDto)
    {
        saleDto.Source = "Receipt";
        var result = await _mediator.Send(new CreateSaleCommand(saleDto));
        
        if (result.IsFailure)
            return BadRequest(result.Error);
        
        return Ok(new { SaleId = result.Value });
    }

    [HttpGet]
    public async Task<IActionResult> GetSales(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int skip = 0,
        [FromQuery] int take = 50)
    {
        var result = await _mediator.Send(new GetSalesQuery(fromDate, toDate, skip, take));
        
        if (result.IsFailure)
            return BadRequest(result.Error);
        
        return Ok(result.Value);
    }
}
