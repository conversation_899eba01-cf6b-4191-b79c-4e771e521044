using RetailInventory.Domain.Enums;

namespace RetailInventory.Domain.ValueObjects;

public record Quantity
{
    public decimal Value { get; init; }
    public UnitOfMeasure Unit { get; init; }

    public Quantity(decimal value, UnitOfMeasure unit)
    {
        if (value < 0)
            throw new ArgumentException("Quantity cannot be negative", nameof(value));
        
        Value = value;
        Unit = unit;
    }

    public static Quantity Zero(UnitOfMeasure unit) => new(0, unit);

    public static Quantity operator +(Quantity left, Quantity right)
    {
        if (left.Unit != right.Unit)
            throw new InvalidOperationException("Cannot add quantities with different units");
        
        return new Quantity(left.Value + right.Value, left.Unit);
    }

    public static Quantity operator -(Quantity left, Quantity right)
    {
        if (left.Unit != right.Unit)
            throw new InvalidOperationException("Cannot subtract quantities with different units");
        
        return new Quantity(left.Value - right.Value, left.Unit);
    }

    public static Quantity operator *(Quantity quantity, decimal multiplier)
    {
        return new Quantity(quantity.Value * multiplier, quantity.Unit);
    }

    public bool IsZero => Value == 0;

    public override string ToString()
    {
        return $"{Value} {Unit}";
    }
}
