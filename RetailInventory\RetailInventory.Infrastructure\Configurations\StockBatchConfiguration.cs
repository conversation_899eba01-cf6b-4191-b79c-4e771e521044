using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RetailInventory.Domain.Entities;

namespace RetailInventory.Infrastructure.Configurations;

public class StockBatchConfiguration : IEntityTypeConfiguration<StockBatch>
{
    public void Configure(EntityTypeBuilder<StockBatch> builder)
    {
        builder.HasKey(sb => sb.Id);

        builder.Property(sb => sb.CostPerUnit)
            .IsRequired()
            .HasPrecision(18, 4);

        builder.Property(sb => sb.Quantity)
            .IsRequired()
            .HasPrecision(18, 4);

        builder.Property(sb => sb.RemainingQuantity)
            .IsRequired()
            .HasPrecision(18, 4);

        builder.Property(sb => sb.ReceivedDate)
            .IsRequired();

        builder.Property(sb => sb.BatchNumber)
            .HasMaxLength(100);

        builder.Property(sb => sb.CreatedAt)
            .IsRequired();

        // Indexes
        builder.HasIndex(sb => sb.ProductId);
        builder.HasIndex(sb => sb.ReceivedDate);
        builder.HasIndex(sb => sb.BatchNumber);
    }
}
