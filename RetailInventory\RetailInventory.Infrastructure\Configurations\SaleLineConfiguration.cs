using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RetailInventory.Domain.Entities;

namespace RetailInventory.Infrastructure.Configurations;

public class SaleLineConfiguration : IEntityTypeConfiguration<SaleLine>
{
    public void Configure(EntityTypeBuilder<SaleLine> builder)
    {
        builder.HasKey(sl => sl.Id);

        builder.Property(sl => sl.Quantity)
            .IsRequired()
            .HasPrecision(18, 4);

        builder.Property(sl => sl.SalePricePerUnit)
            .IsRequired()
            .HasPrecision(18, 4);

        builder.Property(sl => sl.CostPerUnit)
            .IsRequired()
            .HasPrecision(18, 4);

        builder.Property(sl => sl.LineTotal)
            .IsRequired()
            .HasPrecision(18, 4);

        builder.Property(sl => sl.LineProfit)
            .IsRequired()
            .HasPrecision(18, 4);

        // Indexes
        builder.HasIndex(sl => sl.SaleId);
        builder.HasIndex(sl => sl.ProductId);
    }
}
