namespace RetailInventory.Application.DTOs;

public class ParsedReceiptDto
{
    public string ProductType { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public DateTime Date { get; set; }
    public decimal Confidence { get; set; }
    public string Brand { get; set; } = string.Empty;
    public string Size { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public string Notes { get; set; } = string.Empty;
}

public class ReceiptParseResultDto
{
    public bool IsSuccess { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public List<ParsedReceiptDto> ParsedItems { get; set; } = new();
    public decimal TotalAmount { get; set; }
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
    public string ReceiptId { get; set; } = string.Empty;
}

public class ReceiptUploadDto
{
    public string ImageBase64 { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
}
