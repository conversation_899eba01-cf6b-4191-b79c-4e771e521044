using RetailInventory.Application.DTOs;
using RetailInventory.Domain.Interfaces;

namespace RetailInventory.Application.Services;

public class StubReorderRadarService : IReorderRadarService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly Random _random = new();

    public StubReorderRadarService(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<ReorderRadarResultDto> GenerateReorderSuggestionsAsync()
    {
        try
        {
            var products = await _unitOfWork.Products.FindAsync(p => p.IsActive);
            var suggestions = new List<ReorderSuggestionDto>();

            foreach (var product in products)
            {
                var suggestion = await GenerateProductSuggestion(product.Id, product.Name, 
                    product.CurrentQuantity, product.ReorderThreshold, product.UnitOfMeasure.ToString());
                
                if (suggestion != null)
                {
                    suggestions.Add(suggestion);
                }
            }

            // Sort by priority and days until stock out
            suggestions = suggestions
                .OrderByDescending(s => s.Priority)
                .ThenBy(s => s.DaysUntilStockOut)
                .ToList();

            var result = new ReorderRadarResultDto
            {
                Suggestions = suggestions,
                TotalCriticalItems = suggestions.Count(s => s.Priority == ReorderPriority.Critical),
                TotalHighPriorityItems = suggestions.Count(s => s.Priority == ReorderPriority.High),
                TotalEstimatedCost = suggestions.Sum(s => s.EstimatedCost),
                Summary = GenerateSummary(suggestions)
            };

            return result;
        }
        catch (Exception ex)
        {
            throw new ReorderRadarException($"Failed to generate reorder suggestions: {ex.Message}", ex);
        }
    }

    public async Task<List<SalesVelocityDto>> CalculateSalesVelocityAsync()
    {
        await Task.Delay(_random.Next(500, 1000)); // Simulate calculation

        var products = await _unitOfWork.Products.FindAsync(p => p.IsActive);
        var velocities = new List<SalesVelocityDto>();

        foreach (var product in products)
        {
            // Simulate sales velocity calculation
            var last7Days = _random.Next(0, 10) + (_random.NextDouble() * 5);
            var last30Days = _random.Next(0, 8) + (_random.NextDouble() * 4);
            var trendPercentage = ((last7Days - last30Days) / Math.Max(last30Days, 0.1)) * 100;

            velocities.Add(new SalesVelocityDto
            {
                ProductId = product.Id,
                ProductName = product.Name,
                Last7DaysAverage = (decimal)last7Days,
                Last30DaysAverage = (decimal)last30Days,
                TrendPercentage = (decimal)trendPercentage,
                TrendDirection = GetTrendDirection(trendPercentage),
                TotalSalesLast30Days = _random.Next(10, 100)
            });
        }

        return velocities.OrderByDescending(v => v.Last7DaysAverage).ToList();
    }

    public async Task<ReorderSuggestionDto> GetProductReorderSuggestionAsync(int productId)
    {
        var product = await _unitOfWork.Products.GetByIdAsync(productId);
        if (product == null)
            throw new ReorderRadarException($"Product with ID {productId} not found");

        return await GenerateProductSuggestion(product.Id, product.Name, 
            product.CurrentQuantity, product.ReorderThreshold, product.UnitOfMeasure.ToString());
    }

    public async Task<decimal> CalculateOptimalReorderPointAsync(int productId, int leadTimeDays = 7)
    {
        await Task.Delay(_random.Next(200, 500));

        var product = await _unitOfWork.Products.GetByIdAsync(productId);
        if (product == null) return 0;

        // Simulate calculation: (Average daily sales * Lead time) + Safety stock
        var avgDailySales = _random.Next(1, 5) + (_random.NextDouble() * 2);
        var safetyStock = avgDailySales * 0.5; // 50% safety stock
        var optimalReorderPoint = (decimal)(avgDailySales * leadTimeDays + safetyStock);

        return Math.Max(optimalReorderPoint, product.ReorderThreshold);
    }

    public async Task<List<ReorderSuggestionDto>> GetCriticalStockAlertsAsync()
    {
        var allSuggestions = await GenerateReorderSuggestionsAsync();
        return allSuggestions.Suggestions
            .Where(s => s.Priority == ReorderPriority.Critical || s.DaysUntilStockOut <= 3)
            .ToList();
    }

    private async Task<ReorderSuggestionDto?> GenerateProductSuggestion(int productId, string productName, 
        decimal currentStock, decimal reorderThreshold, string unitOfMeasure)
    {
        await Task.Delay(_random.Next(100, 300)); // Simulate AI processing

        // Simulate sales velocity (average daily sales)
        var avgDailySales = _random.Next(1, 8) + (_random.NextDouble() * 3);
        var daysUntilStockOut = avgDailySales > 0 ? (int)(currentStock / (decimal)avgDailySales) : 999;
        
        // Only suggest reorder if below threshold or will run out soon
        if (currentStock > reorderThreshold && daysUntilStockOut > 14)
            return null;

        var priority = CalculatePriority(currentStock, reorderThreshold, daysUntilStockOut);
        var suggestedQuantity = CalculateSuggestedQuantity(currentStock, reorderThreshold, (decimal)avgDailySales);
        var estimatedCost = suggestedQuantity * GetEstimatedCostPerUnit(productName);
        var trendIndicator = GetTrendIndicator(avgDailySales);
        var leadTimeDays = _random.Next(3, 14);

        return new ReorderSuggestionDto
        {
            ProductId = productId,
            ProductName = productName,
            CurrentStock = currentStock,
            ReorderThreshold = reorderThreshold,
            SuggestedOrderQuantity = suggestedQuantity,
            AverageDailySales = (decimal)avgDailySales,
            DaysUntilStockOut = daysUntilStockOut,
            UnitOfMeasure = unitOfMeasure,
            Priority = priority,
            TrendIndicator = trendIndicator,
            EstimatedCost = estimatedCost,
            Reason = GenerateReason(priority, daysUntilStockOut, currentStock, reorderThreshold),
            LastRestocked = DateTime.UtcNow.AddDays(-_random.Next(1, 60)),
            LeadTimeDays = leadTimeDays
        };
    }

    private ReorderPriority CalculatePriority(decimal currentStock, decimal threshold, int daysUntilStockOut)
    {
        if (currentStock <= 0 || daysUntilStockOut <= 1)
            return ReorderPriority.Critical;
        
        if (currentStock <= threshold * 0.5m || daysUntilStockOut <= 3)
            return ReorderPriority.High;
        
        if (currentStock <= threshold || daysUntilStockOut <= 7)
            return ReorderPriority.Medium;
        
        return ReorderPriority.Low;
    }

    private decimal CalculateSuggestedQuantity(decimal currentStock, decimal threshold, decimal avgDailySales)
    {
        // Suggest enough to last 30 days plus safety stock
        var thirtyDaysSupply = avgDailySales * 30;
        var safetyStock = avgDailySales * 7; // 1 week safety stock
        var targetStock = thirtyDaysSupply + safetyStock;
        
        return Math.Max(targetStock - currentStock, threshold * 2);
    }

    private decimal GetEstimatedCostPerUnit(string productName)
    {
        return productName.ToLowerInvariant() switch
        {
            var name when name.Contains("nike") => 3200m,
            var name when name.Contains("adidas") => 4500m,
            var name when name.Contains("formal") => 2200m,
            var name when name.Contains("heels") => 1800m,
            var name when name.Contains("sneakers") => 1200m,
            var name when name.Contains("sports") => 2500m,
            _ => 2000m
        };
    }

    private string GetTrendIndicator(double avgDailySales)
    {
        var trendValue = _random.NextDouble();
        return trendValue switch
        {
            > 0.6 => "↗️", // Increasing demand
            < 0.4 => "↘️", // Decreasing demand
            _ => "➡️"      // Stable demand
        };
    }

    private string GetTrendDirection(double trendPercentage)
    {
        return trendPercentage switch
        {
            > 10 => "Increasing",
            < -10 => "Decreasing",
            _ => "Stable"
        };
    }

    private string GenerateReason(ReorderPriority priority, int daysUntilStockOut, decimal currentStock, decimal threshold)
    {
        return priority switch
        {
            ReorderPriority.Critical => currentStock <= 0 
                ? "Out of stock - immediate reorder required"
                : $"Critical: Only {daysUntilStockOut} days of stock remaining",
            ReorderPriority.High => $"High priority: Stock will run out in {daysUntilStockOut} days",
            ReorderPriority.Medium => $"Below reorder threshold ({threshold}), current stock: {currentStock}",
            _ => "Preventive reorder to maintain optimal stock levels"
        };
    }

    private string GenerateSummary(List<ReorderSuggestionDto> suggestions)
    {
        var critical = suggestions.Count(s => s.Priority == ReorderPriority.Critical);
        var high = suggestions.Count(s => s.Priority == ReorderPriority.High);
        var totalCost = suggestions.Sum(s => s.EstimatedCost);

        if (critical > 0)
            return $"🚨 {critical} critical items need immediate attention. Total estimated cost: ₹{totalCost:N0}";
        
        if (high > 0)
            return $"⚠️ {high} high-priority items should be reordered soon. Total estimated cost: ₹{totalCost:N0}";
        
        return suggestions.Any() 
            ? $"📊 {suggestions.Count} items recommended for reorder. Total estimated cost: ₹{totalCost:N0}"
            : "✅ All products are well-stocked. No immediate reorders needed.";
    }
}
