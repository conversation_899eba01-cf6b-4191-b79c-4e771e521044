using RetailInventory.Application.DTOs;

namespace RetailInventory.Application.Services;

public interface IReceiptParserService
{
    Task<ReceiptParseResultDto> ParseReceiptAsync(ReceiptUploadDto receiptUpload);
    Task<ReceiptParseResultDto> ParseReceiptFromUrlAsync(string imageUrl);
    Task<bool> ValidateReceiptFormatAsync(ReceiptUploadDto receiptUpload);
}

public class ReceiptParsingException : Exception
{
    public ReceiptParsingException(string message) : base(message) { }
    public ReceiptParsingException(string message, Exception innerException) : base(message, innerException) { }
}
