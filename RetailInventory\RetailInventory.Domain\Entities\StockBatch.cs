namespace RetailInventory.Domain.Entities;

public class StockBatch
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public decimal CostPerUnit { get; set; }
    public decimal Quantity { get; set; }
    public decimal RemainingQuantity { get; set; }
    public DateTime ReceivedDate { get; set; } = DateTime.UtcNow;
    public string BatchNumber { get; set; } = string.Empty;
    public int? SupplierId { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual Product Product { get; set; } = null!;

    // Business methods
    public void ReduceQuantity(decimal quantity)
    {
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be positive");
        
        if (RemainingQuantity < quantity)
            throw new InvalidOperationException("Insufficient quantity in batch");
        
        RemainingQuantity -= quantity;
    }

    public bool IsExhausted => RemainingQuantity <= 0;

    public decimal TotalValue => RemainingQuantity * CostPerUnit;

    public int DaysInStock => (DateTime.UtcNow - ReceivedDate).Days;
}
