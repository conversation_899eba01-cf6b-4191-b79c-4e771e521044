using MediatR;
using Microsoft.AspNetCore.Mvc;
using RetailInventory.Application.DTOs;
using RetailInventory.Application.Sales.Commands;
using RetailInventory.Application.Services;

namespace RetailInventory.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AIController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly IReceiptParserService _receiptParser;
    private readonly IVoiceLedgerService _voiceLedger;
    private readonly IReorderRadarService _reorderRadar;

    public AIController(
        IMediator mediator,
        IReceiptParserService receiptParser,
        IVoiceLedgerService voiceLedger,
        IReorderRadarService reorderRadar)
    {
        _mediator = mediator;
        _receiptParser = receiptParser;
        _voiceLedger = voiceLedger;
        _reorderRadar = reorderRadar;
    }

    [HttpPost("receipt/parse")]
    public async Task<IActionResult> ParseReceipt([FromBody] ReceiptUploadDto receiptUpload)
    {
        var result = await _receiptParser.ParseReceiptAsync(receiptUpload);
        
        if (!result.IsSuccess)
            return BadRequest(result);
        
        return Ok(result);
    }

    [HttpPost("receipt/create-sale")]
    public async Task<IActionResult> CreateSaleFromReceipt([FromBody] ReceiptUploadDto receiptUpload)
    {
        var result = await _mediator.Send(new CreateSaleFromReceiptCommand(receiptUpload));
        
        if (result.IsFailure)
            return BadRequest(result.Error);
        
        return Ok(new { SaleId = result.Value, Message = "Sale created successfully from receipt" });
    }

    [HttpPost("voice/process")]
    public async Task<IActionResult> ProcessVoiceCommand([FromBody] VoiceCommandDto voiceCommand)
    {
        var result = await _voiceLedger.ProcessVoiceCommandAsync(voiceCommand);
        
        if (!result.IsSuccess)
            return BadRequest(result);
        
        return Ok(result);
    }

    [HttpPost("voice/text")]
    public async Task<IActionResult> ProcessTextCommand([FromBody] string textCommand)
    {
        var result = await _voiceLedger.ProcessTextCommandAsync(textCommand);
        
        if (!result.IsSuccess)
            return BadRequest(result);
        
        return Ok(result);
    }

    [HttpPost("voice/create-sale")]
    public async Task<IActionResult> CreateSaleFromVoice([FromBody] VoiceCommandDto voiceCommand)
    {
        var result = await _mediator.Send(new CreateSaleFromVoiceCommand(voiceCommand));
        
        if (result.IsFailure)
            return BadRequest(result.Error);
        
        return Ok(new { SaleId = result.Value, Message = "Sale created successfully from voice command" });
    }

    [HttpGet("voice/patterns")]
    public async Task<IActionResult> GetVoicePatterns()
    {
        var patterns = await _voiceLedger.GetSupportedPatternsAsync();
        return Ok(patterns);
    }

    [HttpGet("reorder/suggestions")]
    public async Task<IActionResult> GetReorderSuggestions()
    {
        var result = await _reorderRadar.GenerateReorderSuggestionsAsync();
        return Ok(result);
    }

    [HttpGet("reorder/critical")]
    public async Task<IActionResult> GetCriticalStockAlerts()
    {
        var result = await _reorderRadar.GetCriticalStockAlertsAsync();
        return Ok(result);
    }

    [HttpGet("reorder/velocity")]
    public async Task<IActionResult> GetSalesVelocity()
    {
        var result = await _reorderRadar.CalculateSalesVelocityAsync();
        return Ok(result);
    }

    [HttpGet("reorder/product/{productId}")]
    public async Task<IActionResult> GetProductReorderSuggestion(int productId)
    {
        try
        {
            var result = await _reorderRadar.GetProductReorderSuggestionAsync(productId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return NotFound(ex.Message);
        }
    }
}
