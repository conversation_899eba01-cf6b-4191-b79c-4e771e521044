using MediatR;
using Microsoft.AspNetCore.Mvc;
using RetailInventory.Application.DTOs;
using RetailInventory.Application.Stock.Commands;
using RetailInventory.Application.Stock.Queries;

namespace RetailInventory.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class StockController : ControllerBase
{
    private readonly IMediator _mediator;

    public StockController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost("receive")]
    public async Task<IActionResult> ReceiveStock([FromBody] ReceiveStockDto stockDto)
    {
        var result = await _mediator.Send(new ReceiveStockCommand(stockDto));
        
        if (result.IsFailure)
            return BadRequest(result.Error);
        
        return Ok(new { StockBatchId = result.Value });
    }

    [HttpGet("low-stock")]
    public async Task<IActionResult> GetLowStockItems()
    {
        var result = await _mediator.Send(new GetLowStockItemsQuery());
        
        if (result.IsFailure)
            return BadRequest(result.Error);
        
        return Ok(result.Value);
    }
}
