using MediatR;
using RetailInventory.Application.Common;
using RetailInventory.Application.DTOs;
using RetailInventory.Application.Services;

namespace RetailInventory.Application.Sales.Commands;

public record CreateSaleFromVoiceCommand(VoiceCommandDto VoiceCommand) : BaseCommand<Result<int>>;

public class CreateSaleFromVoiceCommandHandler : IRequestHandler<CreateSaleFromVoiceCommand, Result<int>>
{
    private readonly IVoiceLedgerService _voiceLedger;
    private readonly IMediator _mediator;

    public CreateSaleFromVoiceCommandHandler(IVoiceLedgerService voiceLedger, IMediator mediator)
    {
        _voiceLedger = voiceLedger;
        _mediator = mediator;
    }

    public async Task<Result<int>> Handle(CreateSaleFromVoiceCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Process the voice command
            var voiceResult = await _voiceLedger.ProcessVoiceCommandAsync(request.VoiceCommand);
            
            if (!voiceResult.IsSuccess)
                return Result.Failure<int>($"Voice processing failed: {voiceResult.ErrorMessage}");

            if (voiceResult.ParsedSale == null)
                return Result.Failure<int>("No sale information could be extracted from voice command");

            var parsedSale = voiceResult.ParsedSale;

            // Convert to sale line
            var saleLines = new List<CreateSaleLineDto>
            {
                new CreateSaleLineDto
                {
                    ProductId = 1, // TODO: Map product names to IDs
                    Quantity = parsedSale.Quantity,
                    SalePricePerUnit = parsedSale.PricePerUnit
                }
            };

            // Create the sale
            var createSaleDto = new CreateSaleDto
            {
                SaleDate = DateTime.UtcNow,
                Source = "Voice",
                Notes = $"Voice command: '{voiceResult.TranscribedText}'. Confidence: {voiceResult.Confidence:P0}. {parsedSale.Notes}",
                SaleLines = saleLines
            };

            var saleResult = await _mediator.Send(new CreateSaleCommand(createSaleDto), cancellationToken);
            
            if (saleResult.IsFailure)
                return Result.Failure<int>($"Failed to create sale: {saleResult.Error}");

            return Result.Success(saleResult.Value);
        }
        catch (Exception ex)
        {
            return Result.Failure<int>($"Error processing voice command: {ex.Message}");
        }
    }
}
