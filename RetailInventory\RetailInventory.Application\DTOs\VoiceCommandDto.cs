namespace RetailInventory.Application.DTOs;

public class VoiceCommandDto
{
    public string AudioBase64 { get; set; } = string.Empty;
    public string TranscribedText { get; set; } = string.Empty;
    public string Language { get; set; } = "en-US";
    public DateTime RecordedAt { get; set; } = DateTime.UtcNow;
}

public class VoiceCommandResultDto
{
    public bool IsSuccess { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public string TranscribedText { get; set; } = string.Empty;
    public VoiceParsedSaleDto? ParsedSale { get; set; }
    public decimal Confidence { get; set; }
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
    public string CommandId { get; set; } = string.Empty;
}

public class VoiceParsedSaleDto
{
    public string ProductName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public decimal PricePerUnit { get; set; }
    public string Currency { get; set; } = "INR";
    public string Size { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public string Brand { get; set; } = string.Empty;
    public string Notes { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
}

public class VoiceCommandPatternDto
{
    public string Pattern { get; set; } = string.Empty;
    public string Example { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}
