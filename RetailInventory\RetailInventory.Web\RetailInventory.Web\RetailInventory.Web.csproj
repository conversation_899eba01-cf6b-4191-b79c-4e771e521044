<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\RetailInventory.Web.Client\RetailInventory.Web.Client.csproj" />
    <ProjectReference Include="..\..\RetailInventory.Application\RetailInventory.Application.csproj" />
    <ProjectReference Include="..\..\RetailInventory.Infrastructure\RetailInventory.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.0" />
    <PackageReference Include="MudBlazor" Version="6.11.2" />
  </ItemGroup>

</Project>
