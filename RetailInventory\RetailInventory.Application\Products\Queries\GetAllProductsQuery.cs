using MediatR;
using Microsoft.EntityFrameworkCore;
using RetailInventory.Application.Common;
using RetailInventory.Application.DTOs;
using RetailInventory.Domain.Interfaces;

namespace RetailInventory.Application.Products.Queries;

public record GetAllProductsQuery : BaseQuery<Result<List<ProductDto>>>;

public class GetAllProductsQueryHandler : IRequestHandler<GetAllProductsQuery, Result<List<ProductDto>>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetAllProductsQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<List<ProductDto>>> Handle(GetAllProductsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var products = await _unitOfWork.Products.GetAllAsync();
            
            var productDtos = new List<ProductDto>();
            
            foreach (var product in products)
            {
                var productMeta = await _unitOfWork.ProductMetas.FirstOrDefaultAsync(pm => pm.ProductId == product.Id);
                
                var productDto = new ProductDto
                {
                    Id = product.Id,
                    Name = product.Name,
                    Category = product.Category,
                    Specifications = product.Specifications,
                    Grade = product.Grade,
                    Color = product.Color,
                    CurrentQuantity = product.CurrentQuantity,
                    ReorderThreshold = product.ReorderThreshold,
                    UnitOfMeasure = product.UnitOfMeasure,
                    IsActive = product.IsActive,
                    IsLowStock = product.IsLowStock,
                    AverageCostPerUnit = product.GetAverageCostPerUnit(),
                    CreatedAt = product.CreatedAt,
                    UpdatedAt = product.UpdatedAt,
                    MetadataJson = productMeta?.MetadataJson
                };
                
                productDtos.Add(productDto);
            }

            return Result.Success(productDtos);
        }
        catch (Exception ex)
        {
            return Result.Failure<List<ProductDto>>($"Error retrieving products: {ex.Message}");
        }
    }
}
