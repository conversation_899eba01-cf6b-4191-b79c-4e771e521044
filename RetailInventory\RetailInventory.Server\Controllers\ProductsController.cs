using MediatR;
using Microsoft.AspNetCore.Mvc;
using RetailInventory.Application.DTOs;
using RetailInventory.Application.Products.Commands;
using RetailInventory.Application.Products.Queries;

namespace RetailInventory.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ProductsController : ControllerBase
{
    private readonly IMediator _mediator;

    public ProductsController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    public async Task<IActionResult> GetAllProducts()
    {
        var result = await _mediator.Send(new GetAllProductsQuery());
        
        if (result.IsFailure)
            return BadRequest(result.Error);
        
        return Ok(result.Value);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetProduct(int id)
    {
        var result = await _mediator.Send(new GetProductByIdQuery(id));
        
        if (result.IsFailure)
            return NotFound(result.Error);
        
        return Ok(result.Value);
    }

    [HttpPost]
    public async Task<IActionResult> CreateProduct([FromBody] CreateProductDto productDto)
    {
        var result = await _mediator.Send(new CreateProductCommand(productDto));
        
        if (result.IsFailure)
            return BadRequest(result.Error);
        
        return CreatedAtAction(nameof(GetProduct), new { id = result.Value }, result.Value);
    }
}
