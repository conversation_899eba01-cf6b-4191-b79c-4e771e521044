You are the solo architect & developer.  
Goal: deliver a runnable **local prototype** of an inventory + sales system for
a leather warehouse / shop.  Users can add leather stock in kilograms, record
sales, see automatic cost / profit, and attach rich metadata for future
products (shoes, clothing, bags).

────────────────────────────────────────────────────────────────────────────
1️⃣  DEV ENVIRONMENT ASSUMPTIONS
────────────────────────────────────────────────────────────────────────────
• OS……….. Windows 10/11, VS 2022 or VS Code + C# extension  
• SDK………. .NET 8 (LTS)  
• DB……….. Local SQL Server installed on machine
   • Integrated Authentication  
   • DB name:  InventoryDb  (you will need to create this)
• ORMs……. EF Core 8, Code-First  
• Frontend… Blazor WebAssembly Hosted (ASP.NET Core Hosted template)  
• Auth…….. NONE for now (simple local prototype).  Roles will be added later.  
• AI ………  Stubbed: a local service returns a fake parsed-receipt JSON so the
            rest of the flow works without Azure or file storage.

────────────────────────────────────────────────────────────────────────────
2️⃣  FUNCTIONAL SCOPE  (MVP)
────────────────────────────────────────────────────────────────────────────
■ **Inventory (Leather by kg)**
   - Add / edit leather type  
     • fields: Id, Name, Category (Goat, Cow, Buffalo, Sheep…), Thickness (mm),
       Grade, Colour, CostPerKg (decimal), CurrentQtyKg  
   - Receive stock → increases qty & records CostPerKg (can vary per batch)  
   - Reorder alert threshold (kg)  

■ **Sales**
   - Manual sale form  
     • choose Leather Type  
     • quantity kg, SalePricePerKg  
     • auto-compute revenue, cost, profit  
   - *Optional stub*: “Import sale from receipt” – call fake service that
     returns {leatherId, qty, salePrice, date}.  (No file upload yet.)

■ **Metadata for Future Articles**
   - Separate table **ArticleMeta** with flexible key/value pairs OR
     a JSON column in Leather table (your choice).  Example:
     `{ "IdealFor": ["Shoes", "Belts"], "Finish": "Nappa" }`

■ **Reports**
   - Dashboard (Today’s Sales ₹, Profit ₹, Low-Stock list)  
   - Sales log table  
   - Export Sales & Inventory as CSV (local file download)
   - Various Inventory reports, with age, money locked  and othe fancy stuff

────────────────────────────────────────────────────────────────────────────
3️⃣  DETAILED TASK LIST  (Claude executes sequentially)
────────────────────────────────────────────────────────────────────────────

### 🧩 TASK 1 – Solution Setup & Project Structure
**Goal**: Create clean architecture Blazor WASM solution with proper project structure

1.1  **Create Solution & Core Projects**
     - Create new Blazor WASM Hosted solution named `LeatherInv`
     - Add Class Library projects: `LeatherInv.Domain`, `LeatherInv.Infrastructure`, `LeatherInv.Application`
     - Verify default projects: `LeatherInv.Server` (WebAPI), `LeatherInv.Client` (Blazor WASM)
     - Set up proper project references (Domain → Infrastructure → Application → Server/Client)

1.2  **Configure Project Dependencies**
     - Add EF Core packages to Infrastructure project
     - Add MediatR and FluentValidation packages to Application project
     - Add MudBlazor package to Client project
     - Configure shared DTOs in a Shared project or namespace

1.3  **Folder Structure Setup**
     - Domain: Entities, Enums, Interfaces, ValueObjects
     - Infrastructure: DbContext, Repositories, Configurations
     - Application: Commands, Queries, DTOs, Validators, Services
     - Server: Controllers, Program.cs configuration
     - Client: Pages, Components, Services

**Acceptance Criteria**: Solution builds successfully, all projects reference correctly

### 📐 TASK 2 – Domain Model & Database Setup
**Goal**: Define complete domain model with EF Core configuration and database creation

2.1  **Define Core Entities**
     - `Leather` entity: Id, Name, Category (enum), Thickness, Grade, Colour, CurrentQtyKg, ReorderThresholdKg, IsActive
     - `StockBatch` entity: Id, LeatherId, CostPerKg, QuantityKg, ReceivedDate, BatchNumber, SupplierId (optional)
     - `Sale` entity: Id, SaleDate, TotalAmount, TotalCost, TotalProfit, CustomerId (optional), Notes
     - `SaleLine` entity: Id, SaleId, LeatherId, QuantityKg, SalePricePerKg, CostPerKg, LineTotal, LineProfit
     - `ArticleMeta` entity: Id, LeatherId, MetadataJson (JSON column for flexible key-value pairs)

2.2  **Define Enums and Value Objects**
     - `LeatherCategory` enum: Cow, Goat, Buffalo, Sheep, Exotic
     - `UnitOfMeasure` enum: Kilograms, Pieces (for future shoes/articles)
     - Money value object for currency handling
     - Quantity value object with unit of measure

2.3  **EF Core Configuration**
     - Create `LeatherInventoryDbContext` with DbSets for all entities
     - Configure entity relationships and constraints using Fluent API
     - Set up connection string for SQL Server with Integrated Authentication
     - Configure JSON column for ArticleMeta.MetadataJson
     - Add audit fields (CreatedAt, UpdatedAt) to relevant entities

2.4  **Database Migration & Seed Data**
     - Create initial EF migration with all tables and relationships
     - Create seed data configuration:
       * 3 leather types: Cow Full-Grain (2.5mm, Grade A), Goat Suede (1.8mm, Grade B), Buffalo Pull-Up (3.0mm, Grade A)
       * 2 stock batches per leather type with different costs and quantities
       * 3 sample sales with multiple sale lines
       * Sample article metadata for each leather type
     - Run database update command

**Acceptance Criteria**: Database created successfully, seed data populated, all relationships working

### 🔌 TASK 3 – API Layer with MediatR Pattern
**Goal**: Implement clean API endpoints using MediatR and CQRS pattern

3.1  **Setup MediatR Infrastructure**
     - Configure MediatR in Program.cs with assembly scanning
     - Create base Command and Query classes
     - Set up FluentValidation pipeline behavior
     - Configure AutoMapper for entity-to-DTO mapping

3.2  **Leather Management APIs**
     - `GET /api/leather` - Query to get all leather types with current stock
     - `GET /api/leather/{id}` - Query to get specific leather details
     - `POST /api/leather` - Command to create new leather type
     - `PUT /api/leather/{id}` - Command to update leather details
     - `DELETE /api/leather/{id}` - Command to soft delete leather type

3.3  **Stock Management APIs**
     - `POST /api/stock/receive` - Command to add new stock batch
     - `GET /api/stock/batches/{leatherId}` - Query to get stock batches for leather
     - `GET /api/stock/low-stock` - Query to get items below reorder threshold

3.4  **Sales Management APIs**
     - `POST /api/sales/manual` - Command to create manual sale
     - `POST /api/sales/from-receipt` - Command to create sale from receipt parsing
     - `GET /api/sales` - Query to get sales with pagination and filtering
     - `GET /api/sales/{id}` - Query to get specific sale details

3.5  **Reporting APIs**
     - `GET /api/reports/dashboard` - Query for dashboard metrics (today's sales, profit, low stock count)
     - `GET /api/reports/sales-summary` - Query for sales summary by date range
     - `GET /api/reports/inventory-valuation` - Query for current inventory value

**Acceptance Criteria**: All APIs working, proper validation, error handling, and response formatting

### 🤖 TASK 4 – Receipt Parser Service (Stubbed)
**Goal**: Create stubbed AI receipt parsing service for future integration

4.1  **Define Receipt Parser Interface**
     - Create `IReceiptParserService` interface
     - Define `ParsedReceiptDto` with fields: LeatherType, Quantity, UnitPrice, Date, Confidence
     - Create `ReceiptParsingException` for error handling

4.2  **Implement Stub Service**
     - Create `StubReceiptParserService` implementing the interface
     - Return realistic fake data based on input parameters
     - Simulate processing delay with Task.Delay
     - Include confidence scores and multiple line items

4.3  **Integration with Sales API**
     - Wire stub service into DI container
     - Integrate with `/api/sales/from-receipt` endpoint
     - Add validation for parsed receipt data
     - Handle parsing failures gracefully

**Acceptance Criteria**: Receipt parsing endpoint works with realistic stub data

### 🎨 TASK 5 – Blazor UI
5.1  Design mobile-first pages:  
     • Dashboard.  • Leather List & Add Form.  
     • Stock-In Form.  • Sale Form.  
     • “Import From Receipt” button that calls stub and shows preview.  
5.2  Use **MudBlazor** or **Radzen** for quick components.  
5.3  Show toast notifications on success/fail.

### ⚙️ TASK 6 – CSV Export
6.1  Utility to generate CSV from sales & inventory; return as `File` result.  
6.2  Add “Download” buttons on UI.

### 🧪 TASK 7 – Tests
7.1  xUnit tests for:  
     • Stock deduction logic  
     • Profit calculation  
     • Seed data correctness  

### 🔄 TASK 8 – README & Local Run Script
8.1  Write README with:  
     - Prereqs (SDK, SQL Server, how to enable LocalDB)  
     - `dotnet ef database update`  
     - `dotnet run` instructions  
8.2  Optionally create `launchSettings.json` to auto-start WebApi & WASM.

────────────────────────────────────────────────────────────────────────────
4️⃣  EXECUTION GUIDELINES  (for Claude)
────────────────────────────────────────────────────────────────────────────
• Present each sub-task under an `### ✅ TASK X.Y` header.  
• Include the **actual code** (C# classes, migrations, razor pages, etc.).  
• For long code blocks, use triple backticks and proper language tags.  

────────────────────────────────────────────────────────────────────────────
5️⃣  OPTIONAL QUESTIONS BEFORE START
────────────────────────────────────────────────────────────────────────────
1. Do we need **unit of measure** other than kg  - yes (Number of units for shoes etc))
2. Should we include **GST fields** now or later?  (now)
3. Is **English-only UI** fine for the prototype?  (yes)
4. Any preference between **MudBlazor** and **Radzen**? You analyze and decide

If answers are unclear, assume: only kg, skip GST for now, English-only, use MudBlazor.

Begin with **TASK 1 – Solution Setup**.
