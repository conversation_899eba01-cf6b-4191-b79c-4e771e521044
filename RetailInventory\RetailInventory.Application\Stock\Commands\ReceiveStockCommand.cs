using MediatR;
using RetailInventory.Application.Common;
using RetailInventory.Application.DTOs;
using RetailInventory.Domain.Entities;
using RetailInventory.Domain.Interfaces;

namespace RetailInventory.Application.Stock.Commands;

public record ReceiveStockCommand(ReceiveStockDto StockDto) : BaseCommand<Result<int>>;

public class ReceiveStockCommandHandler : IRequestHandler<ReceiveStockCommand, Result<int>>
{
    private readonly IUnitOfWork _unitOfWork;

    public ReceiveStockCommandHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<int>> Handle(ReceiveStockCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var dto = request.StockDto;

            // Verify product exists
            var product = await _unitOfWork.Products.GetByIdAsync(dto.ProductId);
            if (product == null)
                return Result.Failure<int>("Product not found");

            if (!product.IsActive)
                return Result.Failure<int>("Cannot receive stock for inactive product");

            // Create stock batch
            var stockBatch = new StockBatch
            {
                ProductId = dto.ProductId,
                CostPerUnit = dto.CostPerUnit,
                Quantity = dto.Quantity,
                RemainingQuantity = dto.Quantity,
                BatchNumber = dto.BatchNumber,
                SupplierId = dto.SupplierId,
                ReceivedDate = dto.ReceivedDate ?? DateTime.UtcNow
            };

            await _unitOfWork.StockBatches.AddAsync(stockBatch);

            // Update product quantity
            product.AddStock(dto.Quantity);
            _unitOfWork.Products.Update(product);

            await _unitOfWork.SaveChangesAsync();

            return Result.Success(stockBatch.Id);
        }
        catch (Exception ex)
        {
            return Result.Failure<int>($"Error receiving stock: {ex.Message}");
        }
    }
}
