using RetailInventory.Domain.Entities;

namespace RetailInventory.Domain.Interfaces;

public interface IUnitOfWork : IDisposable
{
    IRepository<Product> Products { get; }
    IRepository<StockBatch> StockBatches { get; }
    IRepository<Sale> Sales { get; }
    IRepository<SaleLine> SaleLines { get; }
    IRepository<ProductMeta> ProductMetas { get; }
    
    Task<int> SaveChangesAsync();
    Task BeginTransactionAsync();
    Task CommitTransactionAsync();
    Task RollbackTransactionAsync();
}
