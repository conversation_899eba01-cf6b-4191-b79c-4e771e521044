using MediatR;
using RetailInventory.Application.Common;
using RetailInventory.Application.DTOs;
using RetailInventory.Domain.Interfaces;

namespace RetailInventory.Application.Products.Queries;

public record GetProductByIdQuery(int Id) : BaseQuery<Result<ProductDto>>;

public class GetProductByIdQueryHandler : IRequestHandler<GetProductByIdQuery, Result<ProductDto>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetProductByIdQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<ProductDto>> Handle(GetProductByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var product = await _unitOfWork.Products.GetByIdAsync(request.Id);
            
            if (product == null)
                return Result.Failure<ProductDto>("Product not found");

            var productMeta = await _unitOfWork.ProductMetas.FirstOrDefaultAsync(pm => pm.ProductId == product.Id);

            var productDto = new ProductDto
            {
                Id = product.Id,
                Name = product.Name,
                Category = product.Category,
                Specifications = product.Specifications,
                Grade = product.Grade,
                Color = product.Color,
                CurrentQuantity = product.CurrentQuantity,
                ReorderThreshold = product.ReorderThreshold,
                UnitOfMeasure = product.UnitOfMeasure,
                IsActive = product.IsActive,
                IsLowStock = product.IsLowStock,
                AverageCostPerUnit = product.GetAverageCostPerUnit(),
                CreatedAt = product.CreatedAt,
                UpdatedAt = product.UpdatedAt,
                MetadataJson = productMeta?.MetadataJson
            };

            return Result.Success(productDto);
        }
        catch (Exception ex)
        {
            return Result.Failure<ProductDto>($"Error retrieving product: {ex.Message}");
        }
    }
}
