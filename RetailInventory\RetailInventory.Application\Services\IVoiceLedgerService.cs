using RetailInventory.Application.DTOs;

namespace RetailInventory.Application.Services;

public interface IVoiceLedgerService
{
    Task<VoiceCommandResultDto> ProcessVoiceCommandAsync(VoiceCommandDto voiceCommand);
    Task<VoiceCommandResultDto> ProcessTextCommandAsync(string textCommand);
    Task<List<VoiceCommandPatternDto>> GetSupportedPatternsAsync();
    Task<string> TranscribeAudioAsync(string audioBase64);
}

public class VoiceLedgerException : Exception
{
    public VoiceLedgerException(string message) : base(message) { }
    public VoiceLedgerException(string message, Exception innerException) : base(message, innerException) { }
}
