namespace RetailInventory.Application.DTOs;

public class StockBatchDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal CostPerUnit { get; set; }
    public decimal Quantity { get; set; }
    public decimal RemainingQuantity { get; set; }
    public DateTime ReceivedDate { get; set; }
    public string BatchNumber { get; set; } = string.Empty;
    public int? SupplierId { get; set; }
    public int DaysInStock { get; set; }
    public decimal TotalValue { get; set; }
    public bool IsExhausted { get; set; }
}

public class ReceiveStockDto
{
    public int ProductId { get; set; }
    public decimal CostPerUnit { get; set; }
    public decimal Quantity { get; set; }
    public string BatchNumber { get; set; } = string.Empty;
    public int? SupplierId { get; set; }
    public DateTime? ReceivedDate { get; set; }
}

public class LowStockItemDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal CurrentQuantity { get; set; }
    public decimal ReorderThreshold { get; set; }
    public string UnitOfMeasure { get; set; } = string.Empty;
    public decimal SuggestedOrderQuantity { get; set; }
}
